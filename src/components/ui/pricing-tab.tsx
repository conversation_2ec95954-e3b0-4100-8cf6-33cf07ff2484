"use client"

import * as React from "react"
import { motion } from "framer-motion"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

interface TabProps {
  text: string
  selected: boolean
  setSelected: (text: string) => void
  discount?: boolean
}

export function Tab({
  text,
  selected,
  setSelected,
  discount = false,
}: TabProps) {
  return (
    <button
      onClick={() => setSelected(text)}
      className={cn(
        "relative px-6 py-3 text-sm font-semibold capitalize transition-all duration-300",
        "hover:text-gray-900",
        selected
          ? "text-gray-900"
          : "text-gray-600",
        discount && "flex items-center justify-center gap-2.5"
      )}
    >
      <span className="relative z-10">{text}</span>
      {selected && (
        <motion.span
          layoutId="tab"
          transition={{ type: "spring", duration: 0.4 }}
          className="absolute inset-0 z-0 rounded-full bg-white shadow-lg border border-gray-200"
        />
      )}
      {discount && (
        <Badge
          variant="secondary"
          className={cn(
            "relative z-10 whitespace-nowrap shadow-none text-xs px-2 py-0.5",
            "bg-green-100 text-green-700 border-green-200",
            selected && "bg-green-100 text-green-700"
          )}
        >
          Save 35%
        </Badge>
      )}
    </button>
  )
}