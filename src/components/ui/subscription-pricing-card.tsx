"use client"

import * as React from "react"
import { Crown, Sparkles, Check, CreditCard, Star, Zap } from "lucide-react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import NumberFlow from "@number-flow/react"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { type SubscriptionPlan } from "@/lib/subscription"

interface SubscriptionPricingCardProps {
  plan: SubscriptionPlan
  paymentFrequency: string
  locale: string
}

export function SubscriptionPricingCard({ 
  plan, 
  paymentFrequency, 
  locale 
}: SubscriptionPricingCardProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = React.useState(false)

  // Calculate price based on frequency
  const getPrice = () => {
    if (paymentFrequency === "yearly") {
      return plan.yearlyPrice || Math.round(plan.price * 12 * 0.65)
    }
    return plan.price
  }

  const price = getPrice()
  const isYearly = paymentFrequency === "yearly"
  const isPopular = plan.popular || plan.id === 'basic'
  const monthlyEquivalent = isYearly ? Math.round(price / 12 * 100) / 100 : price
  const savings = isYearly ? Math.round((1 - (price / (plan.price * 12))) * 100) : 0

  // Get plan icon
  const getPlanIcon = () => {
    switch (plan.id) {
      case 'basic':
        return <Star className="h-6 w-6 text-white" />
      case 'premium':
        return <Crown className="h-6 w-6 text-white" />
      default:
        return <Sparkles className="h-6 w-6 text-white" />
    }
  }

  // Get plan color
  const getPlanColor = () => {
    switch (plan.id) {
      case 'free':
        return 'bg-gradient-to-r from-green-500 to-emerald-500'
      case 'basic':
        return 'bg-gradient-to-r from-blue-500 to-indigo-600'
      case 'premium':
        return 'bg-gradient-to-r from-purple-500 to-pink-500'
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-600'
    }
  }

  const handleSubscribe = async () => {
    // Handle free plan
    if (plan.id === 'free') {
      if (!session) {
        toast.error(locale === 'zh' ? '请先登录后再开始免费试用' : 'Please log in to start your free trial')
        router.push(`/${locale}/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`)
        return
      }
      toast.success(locale === 'zh' ? '免费试用已激活！' : 'Free trial activated!')
      router.push(`/${locale}/upload`)
      return
    }

    // Check login status for paid plans
    if (!session) {
      toast.error(locale === 'zh' ? '请先登录后再进行购买' : 'Please log in to continue with your purchase')
      router.push(`/${locale}/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`)
      return
    }

    setIsLoading(true)

    try {
      // Get the correct Stripe price ID
      const stripePriceId = isYearly ? plan.stripeYearlyPriceId : plan.stripePriceId

      // Validate required data
      if (!session.user?.email) {
        throw new Error(locale === 'zh' ? '用户邮箱信息缺失' : 'User email is missing')
      }

      // Prepare request payload
      const requestPayload = {
        price: price * 100, // Convert to cents
        email: session.user.email,
        productName: `${locale === 'zh' ? plan.nameZh : plan.name} - ${isYearly ? (locale === 'zh' ? '年付' : 'Yearly') : (locale === 'zh' ? '月付' : 'Monthly')}`,
        successUrl: `${window.location.origin}/${locale}/orders?session_id={CHECKOUT_SESSION_ID}&amount=${price}&plan=${plan.id}&frequency=${paymentFrequency}`,
        cancelUrl: `${window.location.origin}/${locale}/#pricing`,
        stripePriceId: stripePriceId,
        planId: plan.id,
        paymentFrequency: paymentFrequency,
      }

      console.log('Creating payment session for plan:', plan.id, 'with payload:', requestPayload)

      // Create Stripe checkout session
      const response = await fetch("/api/stripe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestPayload),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error ||
          (response.status === 503 ?
            (locale === 'zh' ? 'Stripe支付服务暂时不可用' : 'Stripe payment service temporarily unavailable') :
            (locale === 'zh' ? '支付请求失败' : 'Payment request failed')
          )
        throw new Error(errorMessage)
      }

      const responseData = await response.json()
      console.log('Payment session created successfully:', responseData)

      if (responseData.url) {
        // Show success message before redirect
        toast.success(locale === 'zh' ? '正在跳转到支付页面...' : 'Redirecting to payment page...')

        // Small delay to show the message
        setTimeout(() => {
          window.location.href = responseData.url
        }, 500)
      } else {
        throw new Error(locale === 'zh' ? '未收到支付页面链接' : 'No payment URL received')
      }
    } catch (error) {
      console.error("Payment error:", error)

      // Provide more specific error messages
      let errorMessage = locale === 'zh' ? '支付失败，请重试' : 'Payment failed. Please try again.'

      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = locale === 'zh' ? '网络连接失败，请检查网络后重试' : 'Network connection failed. Please check your connection and try again.'
        } else if (error.message.includes('Stripe')) {
          errorMessage = locale === 'zh' ? 'Stripe支付服务异常，请稍后重试' : 'Stripe payment service error. Please try again later.'
        } else {
          errorMessage = error.message
        }
      }

      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card
      className={cn(
        "relative flex flex-col h-full overflow-hidden transition-all duration-500 ease-out",
        "border-2 border-transparent bg-white/80 backdrop-blur-sm",
        "hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2",
        "group cursor-pointer w-full max-w-sm mx-auto",
        isPopular && [
          "border-gradient-to-r from-purple-500 to-pink-500",
          "ring-2 ring-purple-500/20 ring-offset-2",
          "shadow-xl shadow-purple-500/20",
          "md:scale-105 lg:scale-110",
          "bg-gradient-to-br from-purple-50/50 to-pink-50/50"
        ],
        plan.id === 'free' && [
          "border-green-200",
          "hover:border-green-300",
          "hover:shadow-green-500/10"
        ]
      )}
    >
      {/* Popular badge */}
      {isPopular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
          <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg px-4 py-1.5 text-sm font-semibold">
            <Star className="h-3.5 w-3.5 mr-1.5" />
            {locale === 'zh' ? '最受欢迎' : 'Most Popular'}
          </Badge>
        </div>
      )}

      {/* Gradient overlay for popular plan */}
      {isPopular && (
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 pointer-events-none" />
      )}

      {/* Card content wrapper */}
      <div className="relative z-10 flex flex-col h-full p-8">{/* Content will continue here */}

        {/* Plan icon and name */}
        <div className="text-center mb-6">
          <div className={cn(
            "w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4",
            "shadow-lg transition-transform duration-300 group-hover:scale-110",
            getPlanColor()
          )}>
            {getPlanIcon()}
          </div>
          <h3 className="text-2xl font-bold mb-2 text-gray-900">
            {locale === 'zh' ? plan.nameZh : plan.name}
          </h3>
          {plan.id === 'free' && (
            <p className="text-sm text-gray-600">
              {locale === 'zh' ? '开始您的AI婚纱照之旅' : 'Start your AI wedding photo journey'}
            </p>
          )}
        </div>

        {/* Price */}
        <div className="text-center mb-6">
          <div className="relative">
            {plan.id === 'free' ? (
              <div className="mb-4">
                <div className="text-5xl font-bold text-green-600 mb-2">
                  {locale === 'zh' ? '免费' : 'Free'}
                </div>
                <p className="text-lg text-gray-600">
                  {locale === 'zh' ? '永久免费试用' : 'Forever free trial'}
                </p>
              </div>
            ) : (
              <div className="mb-4">
                <div className="flex items-baseline justify-center mb-2">
                  <NumberFlow
                    format={{
                      style: "currency",
                      currency: "USD",
                      trailingZeroDisplay: "stripIfInteger",
                    }}
                    value={price}
                    className="text-5xl font-bold text-gray-900"
                  />
                  <span className="text-lg text-gray-600 ml-1">
                    {isYearly
                      ? (locale === 'zh' ? '/年' : '/year')
                      : (locale === 'zh' ? '/月' : '/month')
                    }
                  </span>
                </div>
                {isYearly && savings > 0 && (
                  <div className="space-y-2">
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800 border-green-200 px-3 py-1"
                    >
                      {locale === 'zh' ? `节省 ${savings}%` : `Save ${savings}%`}
                    </Badge>
                    <p className="text-sm text-gray-600">
                      {locale === 'zh'
                        ? `相当于每月 $${monthlyEquivalent}`
                        : `Equivalent to $${monthlyEquivalent}/month`
                      }
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Credits */}
        <div className="text-center mb-6">
          <div className={cn(
            "inline-flex items-center justify-center px-4 py-3 rounded-xl",
            "bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200",
            "transition-all duration-300 group-hover:shadow-md"
          )}>
            <Zap className="h-5 w-5 mr-2 text-yellow-600" />
            <span className="font-semibold text-gray-900">
              {plan.credits === 150
                ? (locale === 'zh' ? '无限' : 'Unlimited')
                : plan.credits
              } {locale === 'zh' ? '积分' : 'Credits'}
            </span>
          </div>
        </div>

        {/* Features */}
        <div className="flex-1 mb-8">
          <div className="space-y-4">
            {(locale === 'zh' ? plan.featuresZh : plan.features).slice(0, 6).map((feature, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-0.5">
                  <Check className="h-3 w-3 text-green-600" />
                </div>
                <span className="text-sm text-gray-700 leading-relaxed">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Subscribe button */}
        <Button
          size="lg"
          className={cn(
            "w-full h-12 text-base font-semibold transition-all duration-300",
            "shadow-lg hover:shadow-xl transform hover:scale-105",
            plan.id === 'free' && [
              "bg-gradient-to-r from-green-500 to-emerald-500",
              "hover:from-green-600 hover:to-emerald-600",
              "text-white border-0"
            ],
            isPopular && plan.id !== 'free' && [
              "bg-gradient-to-r from-purple-500 to-pink-500",
              "hover:from-purple-600 hover:to-pink-600",
              "text-white border-0 shadow-purple-500/25"
            ],
            !isPopular && plan.id !== 'free' && [
              "bg-gray-900 hover:bg-gray-800",
              "text-white border-0"
            ]
          )}
          onClick={handleSubscribe}
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-3"></div>
              <span>{locale === 'zh' ? '处理中...' : 'Processing...'}</span>
            </div>
          ) : (
            <div className="flex items-center justify-center">
              {plan.id === 'free' ? (
                <>
                  <Sparkles className="h-5 w-5 mr-2" />
                  <span>{locale === 'zh' ? '开始免费试用' : 'Start Free Trial'}</span>
                </>
              ) : (
                <>
                  <CreditCard className="h-5 w-5 mr-2" />
                  <span>{locale === 'zh' ? '立即订阅' : 'Subscribe Now'}</span>
                </>
              )}
            </div>
          )}
        </Button>
      </div>
    </Card>
  )
}
