"use client"

import * as React from "react"
import { Crown, Sparkles, Check, CreditCard, Star, Zap } from "lucide-react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import NumberFlow from "@number-flow/react"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { type SubscriptionPlan } from "@/lib/subscription"

interface SubscriptionPricingCardProps {
  plan: SubscriptionPlan
  paymentFrequency: string
  locale: string
}

export function SubscriptionPricingCard({ 
  plan, 
  paymentFrequency, 
  locale 
}: SubscriptionPricingCardProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = React.useState(false)

  // Calculate price based on frequency
  const getPrice = () => {
    if (paymentFrequency === "yearly") {
      return plan.yearlyPrice || Math.round(plan.price * 12 * 0.65)
    }
    return plan.price
  }

  const price = getPrice()
  const isYearly = paymentFrequency === "yearly"
  const isPopular = plan.popular || plan.id === 'basic'
  const monthlyEquivalent = isYearly ? Math.round(price / 12 * 100) / 100 : price
  const savings = isYearly ? Math.round((1 - (price / (plan.price * 12))) * 100) : 0

  // Get plan icon
  const getPlanIcon = () => {
    switch (plan.id) {
      case 'basic':
        return <Star className="h-6 w-6 text-white" />
      case 'premium':
        return <Crown className="h-6 w-6 text-white" />
      default:
        return <Sparkles className="h-6 w-6 text-white" />
    }
  }

  // Get plan color
  const getPlanColor = () => {
    switch (plan.id) {
      case 'basic':
        return 'bg-gradient-to-r from-blue-500 to-blue-600'
      case 'premium':
        return 'bg-gradient-to-r from-purple-500 to-pink-500'
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-600'
    }
  }

  const handleSubscribe = async () => {
    // Handle free plan
    if (plan.id === 'free') {
      if (!session) {
        toast.error(locale === 'zh' ? '请先登录后再开始免费试用' : 'Please log in to start your free trial')
        router.push(`/${locale}/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`)
        return
      }
      toast.success(locale === 'zh' ? '免费试用已激活！' : 'Free trial activated!')
      router.push(`/${locale}/upload`)
      return
    }

    // Check login status for paid plans
    if (!session) {
      toast.error(locale === 'zh' ? '请先登录后再进行购买' : 'Please log in to continue with your purchase')
      router.push(`/${locale}/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`)
      return
    }

    setIsLoading(true)

    try {
      // Get the correct Stripe price ID
      const stripePriceId = isYearly ? plan.stripeYearlyPriceId : plan.stripePriceId

      // Create Stripe checkout session
      const response = await fetch("/api/stripe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          price: price * 100, // Convert to cents
          email: session.user?.email,
          productName: `${locale === 'zh' ? plan.nameZh : plan.name} - ${isYearly ? (locale === 'zh' ? '年付' : 'Yearly') : (locale === 'zh' ? '月付' : 'Monthly')}`,
          successUrl: `${window.location.origin}/${locale}/orders?session_id={CHECKOUT_SESSION_ID}&amount=${price}&plan=${plan.id}&frequency=${paymentFrequency}`,
          cancelUrl: `${window.location.origin}/${locale}/#pricing`,
          stripePriceId: stripePriceId, // Pass the Stripe price ID for subscription mode
          planId: plan.id,
          paymentFrequency: paymentFrequency,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || (locale === 'zh' ? '支付请求失败' : 'Payment request failed'))
      }

      const { url } = await response.json()
      if (url) {
        window.location.href = url
      } else {
        throw new Error(locale === 'zh' ? '未收到结账 URL' : 'No checkout URL received')
      }
    } catch (error) {
      console.error("Payment error:", error)
      toast.error(error instanceof Error ? error.message : (locale === 'zh' ? '支付失败，请重试' : 'Payment failed. Please try again.'))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card
      className={cn(
        "relative flex flex-col gap-6 overflow-hidden p-6 transition-all duration-300 hover:shadow-lg hover:scale-105",
        isPopular && "ring-2 ring-purple-500 shadow-xl scale-105 bg-gradient-to-br from-purple-50 to-pink-50"
      )}
    >
      {/* Popular badge */}
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
          <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg">
            <Star className="h-3 w-3 mr-1" />
            {locale === 'zh' ? '最受欢迎' : 'Most Popular'}
          </Badge>
        </div>
      )}

      {/* Background decoration for popular plan */}
      {isPopular && (
        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/20 to-pink-100/20 pointer-events-none" />
      )}

      {/* Plan icon and name */}
      <div className="text-center">
        <div className={cn("w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4", getPlanColor())}>
          {getPlanIcon()}
        </div>
        <h3 className="text-xl font-semibold mb-2">
          {locale === 'zh' ? plan.nameZh : plan.name}
        </h3>
      </div>

      {/* Price */}
      <div className="text-center">
        <div className="relative h-20 flex flex-col items-center justify-center">
          <NumberFlow
            format={{
              style: "currency",
              currency: "USD",
              trailingZeroDisplay: "stripIfInteger",
            }}
            value={price}
            className="text-3xl font-bold"
          />
          <p className="text-sm text-muted-foreground">
            {isYearly
              ? (locale === 'zh' ? '/年' : '/year')
              : (locale === 'zh' ? '/月' : '/month')
            }
          </p>
          {isYearly && (
            <div className="flex flex-col items-center mt-1">
              <Badge variant="secondary" className="mb-1">
                {locale === 'zh' ? `节省 ${savings}%` : `Save ${savings}%`}
              </Badge>
              <p className="text-xs text-muted-foreground">
                {locale === 'zh' ? `相当于每月 $${monthlyEquivalent}` : `Equivalent to $${monthlyEquivalent}/month`}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Credits */}
      <div className="text-center p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-center">
          <Zap className="h-4 w-4 mr-2 text-yellow-500" />
          <span className="font-medium">
            {plan.credits === 150 ? (locale === 'zh' ? '无限' : 'Unlimited') : plan.credits} {locale === 'zh' ? '积分' : 'Credits'}
          </span>
        </div>
      </div>

      {/* Features */}
      <div className="flex-1 space-y-3">
        {(locale === 'zh' ? plan.featuresZh : plan.features).slice(0, 5).map((feature, index) => (
          <div key={index} className="flex items-start text-sm">
            <Check className="h-4 w-4 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
            <span>{feature}</span>
          </div>
        ))}
      </div>

      {/* Subscribe button */}
      <Button
        className={cn(
          "w-full",
          plan.id === 'free'
            ? "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
            : isPopular
              ? "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              : ""
        )}
        onClick={handleSubscribe}
        disabled={isLoading}
      >
        {isLoading ? (
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {locale === 'zh' ? '处理中...' : 'Processing...'}
          </div>
        ) : (
          <div className="flex items-center">
            {plan.id === 'free' ? (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                {locale === 'zh' ? '开始免费试用' : 'Start Free Trial'}
              </>
            ) : (
              <>
                <CreditCard className="h-4 w-4 mr-2" />
                {locale === 'zh' ? '立即订阅' : 'Subscribe Now'}
              </>
            )}
          </div>
        )}
      </Button>
    </Card>
  )
}
