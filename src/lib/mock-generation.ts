// 模拟AI生成服务 - Mock AI generation service for development
// Mock AI generation service for development with high-quality placeholder images

export interface MockGeneratedPhoto {
  id: string;
  style: string;
  styleName: string;
  imageUrl: string;
  originalPhoto: string;
  createdAt: string;
  userId?: string | null;
  generationMode?: 'face_swap' | 'standard';
  faceSwapEnabled?: boolean;
  quality?: 'excellent' | 'good' | 'fair';
  metadata?: {
    prompt?: string;
    processingTime?: number;
    aiModel?: string;
  };
}

// 高质量婚纱照风格模板 - High-quality wedding photo style templates
const STYLE_TEMPLATES = {
  'chinese-traditional': {
    name: '中式传统',
    colors: ['#DC143C', '#FFD700', '#8B0000'],
    description: '优雅传统中式婚纱，红金配色，凤冠霞帔',
    keywords: ['传统', '红色', '金色', '优雅', '古典']
  },
  'western-elegant': {
    name: '西式优雅',
    colors: ['#FFFFFF', '#F8F8FF', '#E6E6FA'],
    description: '经典白色婚纱，精致西式造型，教堂背景',
    keywords: ['经典', '白色', '优雅', '浪漫', '教堂']
  },
  'beach-sunset': {
    name: '海滩日落',
    colors: ['#FF6347', '#FFD700', '#87CEEB'],
    description: '浪漫海滩婚礼，金色日落光线，海浪背景',
    keywords: ['海滩', '日落', '浪漫', '自然', '金色']
  },
  'forest-romantic': {
    name: '森林浪漫',
    colors: ['#228B22', '#90EE90', '#FFFFFF'],
    description: '梦幻森林背景，自然光线，花朵装饰',
    keywords: ['森林', '自然', '梦幻', '绿色', '花朵']
  },
  'vintage-classic': {
    name: '复古经典',
    colors: ['#F5F5DC', '#D2B48C', '#8B4513'],
    description: '永恒复古婚纱风格，珍珠装饰，黑白色调',
    keywords: ['复古', '经典', '珍珠', '黑白', '怀旧']
  },
  'modern-chic': {
    name: '现代时尚',
    colors: ['#C0C0C0', '#FFFFFF', '#000000'],
    description: '现代婚纱风格，简洁线条，都市背景',
    keywords: ['现代', '时尚', '简约', '都市', '线条']
  }
};

// 生成高质量SVG婚纱照占位图 - Generate high-quality SVG wedding photo placeholders
function generateWeddingPhotoSVG(style: string, styleName: string, index: number): string {
  const template = STYLE_TEMPLATES[style as keyof typeof STYLE_TEMPLATES];
  if (!template) {
    return generateDefaultSVG(styleName, index);
  }

  const [primaryColor, secondaryColor, accentColor] = template.colors;
  const width = 400;
  const height = 600;
  
  // 创建渐变背景 - Create gradient background
  const gradientId = `gradient-${style}-${index}`;
  
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${primaryColor};stop-opacity:0.8" />
          <stop offset="50%" style="stop-color:${secondaryColor};stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:${accentColor};stop-opacity:0.4" />
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="2" dy="4" stdDeviation="3" flood-opacity="0.3"/>
        </filter>
      </defs>
      
      <!-- 背景 Background -->
      <rect width="100%" height="100%" fill="url(#${gradientId})"/>
      
      <!-- 装饰元素 Decorative elements -->
      <circle cx="80" cy="100" r="30" fill="${secondaryColor}" opacity="0.3"/>
      <circle cx="320" cy="150" r="25" fill="${accentColor}" opacity="0.4"/>
      <circle cx="150" cy="450" r="35" fill="${primaryColor}" opacity="0.2"/>
      
      <!-- 主要内容区域 Main content area -->
      <rect x="50" y="150" width="300" height="350" rx="20" fill="white" opacity="0.9" filter="url(#shadow)"/>
      
      <!-- 人物轮廓 Figure silhouette -->
      <ellipse cx="200" cy="250" rx="60" ry="80" fill="${primaryColor}" opacity="0.6"/>
      <circle cx="200" cy="200" r="35" fill="${secondaryColor}" opacity="0.7"/>
      
      <!-- 婚纱轮廓 Wedding dress silhouette -->
      <path d="M 140 280 Q 200 320 260 280 Q 280 400 200 450 Q 120 400 140 280" 
            fill="${accentColor}" opacity="0.5"/>
      
      <!-- 标题文字 Title text -->
      <text x="200" y="80" text-anchor="middle" font-family="serif" font-size="24" 
            font-weight="bold" fill="${primaryColor}">
        ${styleName}
      </text>
      
      <!-- 副标题 Subtitle -->
      <text x="200" y="110" text-anchor="middle" font-family="sans-serif" font-size="14" 
            fill="${secondaryColor}">
        AI Wedding Photography
      </text>
      
      <!-- 风格描述 Style description -->
      <text x="200" y="540" text-anchor="middle" font-family="sans-serif" font-size="12" 
            fill="${primaryColor}" opacity="0.8">
        ${template.description}
      </text>
      
      <!-- 装饰性边框 Decorative border -->
      <rect x="10" y="10" width="380" height="580" rx="15" fill="none" 
            stroke="${primaryColor}" stroke-width="2" opacity="0.3"/>
      
      <!-- 质量标记 Quality indicator -->
      <circle cx="350" cy="50" r="20" fill="${secondaryColor}" opacity="0.8"/>
      <text x="350" y="55" text-anchor="middle" font-family="sans-serif" font-size="12" 
            font-weight="bold" fill="white">
        HD
      </text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
}

// 生成默认SVG - Generate default SVG
function generateDefaultSVG(styleName: string, index: number): string {
  const colors = ['#FF69B4', '#FFB6C1', '#FFC0CB'];
  const svg = `
    <svg width="400" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="defaultGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${colors[0]};stop-opacity:0.8" />
          <stop offset="100%" style="stop-color:${colors[2]};stop-opacity:0.4" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#defaultGrad)"/>
      <text x="200" y="300" text-anchor="middle" font-family="serif" font-size="20" 
            font-weight="bold" fill="white">
        ${styleName}
      </text>
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
}

// 模拟生成延迟 - Simulate generation delay
function simulateProcessingDelay(style: string): number {
  const baseDelay = 1000; // 1 second base delay
  const styleComplexity = {
    'chinese-traditional': 1.5,
    'western-elegant': 1.2,
    'beach-sunset': 1.8,
    'forest-romantic': 1.6,
    'vintage-classic': 1.4,
    'modern-chic': 1.0
  };
  
  const complexity = styleComplexity[style as keyof typeof styleComplexity] || 1.0;
  return baseDelay * complexity + Math.random() * 1000; // Add some randomness
}

// 主要的模拟生成函数 - Main mock generation function
export async function generateMockWeddingPhotos(
  photoUrl: string,
  styles: string[],
  userId?: string | null
): Promise<MockGeneratedPhoto[]> {
  console.log('🎭 [MockGeneration] Starting mock wedding photo generation');
  console.log('📋 [MockGeneration] Request details:', {
    photoUrl: photoUrl ? `${photoUrl.substring(0, 50)}...` : 'missing',
    stylesCount: styles.length,
    styles,
    userId
  });

  const generatedPhotos: MockGeneratedPhoto[] = [];

  // 为每个风格生成照片 - Generate photo for each style
  for (let i = 0; i < styles.length; i++) {
    const style = styles[i];
    const template = STYLE_TEMPLATES[style as keyof typeof STYLE_TEMPLATES];
    const styleName = template?.name || style;

    console.log(`🎨 [MockGeneration] Generating photo ${i + 1}/${styles.length} for style: ${style}`);

    // 模拟处理时间 - Simulate processing time
    const processingTime = simulateProcessingDelay(style);
    await new Promise(resolve => setTimeout(resolve, processingTime));

    // 生成高质量占位图 - Generate high-quality placeholder
    const imageUrl = generateWeddingPhotoSVG(style, styleName, i);

    const photoData: MockGeneratedPhoto = {
      id: `mock-photo-${Date.now()}-${i}`,
      style: style,
      styleName: styleName,
      imageUrl: imageUrl,
      originalPhoto: photoUrl,
      createdAt: new Date().toISOString(),
      userId: userId || null,
      generationMode: 'face_swap',
      faceSwapEnabled: true,
      quality: 'excellent',
      metadata: {
        prompt: `Professional wedding photography: ${template?.description || styleName}`,
        processingTime: Math.round(processingTime),
        aiModel: 'mock-dall-e-3'
      }
    };

    generatedPhotos.push(photoData);
    console.log(`✅ [MockGeneration] Generated mock photo for style: ${style}`);
  }

  console.log(`🎉 [MockGeneration] Completed mock generation for all styles. Generated ${generatedPhotos.length} photos`);
  
  return generatedPhotos;
}

// 检查是否应该使用模拟数据 - Check if should use mock data
export function shouldUseMockGeneration(): boolean {
  // 在开发环境中或设置了USE_MOCK_GENERATION环境变量时使用模拟数据
  // Use mock data in development environment or when USE_MOCK_GENERATION is set
  return process.env.NODE_ENV === 'development' || 
         process.env.USE_MOCK_GENERATION === 'true' ||
         process.env.USE_PLACEHOLDER_IMAGES === 'true';
}

// 导出风格模板供其他组件使用 - Export style templates for other components
export { STYLE_TEMPLATES };
