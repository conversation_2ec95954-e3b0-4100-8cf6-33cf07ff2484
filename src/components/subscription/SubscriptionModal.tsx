"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Crown,
  Sparkles,
  Check,
  X,
  CreditCard,
  Star,
  Zap,
  Shield,
  Heart
} from "lucide-react";
import { SUBSCRIPTION_PLANS, getUserSubscription, upgradeSubscription, type SubscriptionPlan, type UserSubscription } from "@/lib/subscription";

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onSubscriptionChange?: (subscription: UserSubscription) => void;
  requiredCredits?: number;
}

// 订阅模态框组件 - Subscription modal component
export function SubscriptionModal({
  isOpen,
  onClose,
  userId,
  onSubscriptionChange,
  requiredCredits = 1
}: SubscriptionModalProps) {
  // 状态管理 - State management
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<string>('basic');
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // 加载当前订阅信息 - Load current subscription info
  useEffect(() => {
    if (isOpen && userId) {
      loadSubscription();
    }
  }, [isOpen, userId]);

  const loadSubscription = async () => {
    try {
      const subscription = await getUserSubscription(userId);
      setCurrentSubscription(subscription);
    } catch (error) {
      console.error('Error loading subscription:', error);
    }
  };

  // 处理订阅升级 - Handle subscription upgrade
  const handleUpgrade = async (planId: string) => {
    setIsUpgrading(true);
    
    try {
      const success = await upgradeSubscription(userId, planId);
      
      if (success) {
        const newSubscription = await getUserSubscription(userId);
        setCurrentSubscription(newSubscription);
        setShowSuccess(true);
        
        if (onSubscriptionChange && newSubscription) {
          onSubscriptionChange(newSubscription);
        }
        
        // 显示成功消息后关闭模态框 - Close modal after showing success message
        setTimeout(() => {
          setShowSuccess(false);
          onClose();
        }, 2000);
      } else {
        alert('升级失败，请重试');
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      alert('升级失败，请重试');
    } finally {
      setIsUpgrading(false);
    }
  };

  // 获取计划图标 - Get plan icon
  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free': return <Heart className="h-5 w-5" />;
      case 'basic': return <Sparkles className="h-5 w-5" />;
      case 'pro': return <Crown className="h-5 w-5" />;
      case 'premium': return <Star className="h-5 w-5" />;
      default: return <Sparkles className="h-5 w-5" />;
    }
  };

  // 获取计划颜色 - Get plan color
  const getPlanColor = (planId: string) => {
    switch (planId) {
      case 'free': return 'text-gray-600 bg-gray-100';
      case 'basic': return 'text-blue-600 bg-blue-100';
      case 'pro': return 'text-purple-600 bg-purple-100';
      case 'premium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (showSuccess) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold mb-2">升级成功！</h3>
            <p className="text-gray-600">您的订阅已成功升级，现在可以享受更多功能了。</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-2xl">
            <Crown className="h-6 w-6 mr-2 text-yellow-500" />
            选择您的订阅计划
          </DialogTitle>
          <DialogDescription>
            升级到高级计划，解锁更多AI婚纱照生成功能和专业特性
          </DialogDescription>
        </DialogHeader>

        {/* 当前订阅状态 - Current subscription status */}
        {currentSubscription && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-blue-800">当前订阅</h4>
                <p className="text-blue-600">
                  {SUBSCRIPTION_PLANS.find(p => p.id === currentSubscription.planId)?.nameZh} - 
                  剩余积分: {currentSubscription.credits}
                </p>
              </div>
              {currentSubscription.credits < requiredCredits && (
                <Badge className="bg-red-100 text-red-700 border-red-200">
                  积分不足
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* 订阅计划网格 - Subscription plans grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {SUBSCRIPTION_PLANS.map((plan) => {
            const isCurrentPlan = currentSubscription?.planId === plan.id;
            const isSelected = selectedPlan === plan.id;
            
            return (
              <Card
                key={plan.id}
                className={`relative p-6 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'ring-2 ring-purple-500 shadow-lg'
                    : 'hover:shadow-md'
                } ${isCurrentPlan ? 'bg-blue-50 border-blue-200' : ''}`}
                onClick={() => setSelectedPlan(plan.id)}
              >
                {/* 热门标签 - Popular badge */}
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                      <Star className="h-3 w-3 mr-1" />
                      最受欢迎
                    </Badge>
                  </div>
                )}

                {/* 当前计划标签 - Current plan badge */}
                {isCurrentPlan && (
                  <div className="absolute -top-3 right-4">
                    <Badge className="bg-blue-500 text-white">
                      当前计划
                    </Badge>
                  </div>
                )}

                <div className="text-center">
                  {/* 计划图标 - Plan icon */}
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 ${getPlanColor(plan.id)}`}>
                    {getPlanIcon(plan.id)}
                  </div>

                  {/* 计划名称和价格 - Plan name and price */}
                  <h3 className="text-lg font-semibold mb-2">{plan.nameZh}</h3>
                  <div className="mb-4">
                    {plan.price === 0 ? (
                      <span className="text-2xl font-bold text-green-600">免费</span>
                    ) : (
                      <div>
                        <span className="text-2xl font-bold">${plan.price}</span>
                        <span className="text-gray-500">/月</span>
                      </div>
                    )}
                  </div>

                  {/* 积分数量 - Credits amount */}
                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-center">
                      <Zap className="h-4 w-4 mr-2 text-yellow-500" />
                      <span className="font-medium">
                        {plan.credits === 150 ? '无限' : plan.credits} 积分
                      </span>
                    </div>
                  </div>

                  {/* 功能列表 - Features list */}
                  <div className="space-y-2 mb-6">
                    {plan.featuresZh.slice(0, 4).map((feature, index) => (
                      <div key={index} className="flex items-start text-sm">
                        <Check className="h-4 w-4 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
                        <span className="text-left">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* 选择按钮 - Select button */}
                  {!isCurrentPlan && (
                    <Button
                      className={`w-full ${
                        plan.popular
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
                          : ''
                      }`}
                      variant={plan.popular ? 'default' : 'outline'}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpgrade(plan.id);
                      }}
                      disabled={isUpgrading}
                    >
                      {isUpgrading ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          升级中...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <CreditCard className="h-4 w-4 mr-2" />
                          {plan.price === 0 ? '选择免费版' : '立即升级'}
                        </div>
                      )}
                    </Button>
                  )}
                </div>
              </Card>
            );
          })}
        </div>

        {/* 安全保障 - Security guarantee */}
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center justify-center text-green-700">
            <Shield className="h-5 w-5 mr-2" />
            <span className="text-sm">
              安全支付 • 随时取消 • 30天退款保证
            </span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
