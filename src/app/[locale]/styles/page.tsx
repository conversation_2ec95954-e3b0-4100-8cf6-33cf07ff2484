"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Check, Sparkles, Eye, Heart, Star, Palette, Camera, Crown, AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { CreditDisplay } from "@/components/subscription/CreditDisplay";
import { SubscriptionModal } from "@/components/subscription/SubscriptionModal";
import { hasEnoughCredits } from "@/lib/subscription";

// 增强的风格选择页面组件
// Enhanced style selection page component with interactive previews and better UX
export default function StylesPage() {
  // 状态管理 - State management
  const [selectedStyles, setSelectedStyles] = useState<string[]>([]);
  const [uploadedPhoto, setUploadedPhoto] = useState<string | null>(null);
  const [photoMetadata, setPhotoMetadata] = useState<any>(null);
  const [hoveredStyle, setHoveredStyle] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState<'grid' | 'list'>('grid');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [hasCredits, setHasCredits] = useState(true);

  // 引用和路由 - Refs and routing
  const router = useRouter();
  const t = useTranslations("photoAI.styles");

  // 模拟用户ID（在实际应用中应该从认证系统获取）- Mock user ID (should come from auth system in real app)
  const userId = "user_demo_123";

  // 增强的风格数据，包含预览图片和详细信息 - Enhanced style data with preview images and details
  const enhancedStyles = [
    {
      id: "chinese-traditional",
      name: "中式传统",
      description: "优雅的传统中式婚纱，经典的红金配色",
      previewImage: "/placeholder.svg?height=400&width=300&text=Chinese+Traditional",
      popularity: 95,
      difficulty: "简单",
      estimatedTime: "2-3分钟",
      tags: ["传统", "优雅", "红色", "金色"],
      features: ["凤冠霞帔", "传统刺绣", "古典背景"]
    },
    {
      id: "western-elegant",
      name: "西式优雅",
      description: "经典白色婚纱，精致的西式造型",
      previewImage: "/placeholder.svg?height=400&width=300&text=Western+Elegant",
      popularity: 92,
      difficulty: "简单",
      estimatedTime: "2-3分钟",
      tags: ["经典", "白色", "优雅", "浪漫"],
      features: ["蕾丝细节", "拖尾设计", "教堂背景"]
    },
    {
      id: "beach-sunset",
      name: "海滩日落",
      description: "浪漫的海滩婚礼，金色日落光线",
      previewImage: "/placeholder.svg?height=400&width=300&text=Beach+Sunset",
      popularity: 88,
      difficulty: "中等",
      estimatedTime: "3-4分钟",
      tags: ["海滩", "日落", "自然", "浪漫"],
      features: ["海浪背景", "金色光线", "飘逸婚纱"]
    },
    {
      id: "forest-romantic",
      name: "森林浪漫",
      description: "梦幻森林背景，自然光线和浪漫氛围",
      previewImage: "/placeholder.svg?height=400&width=300&text=Forest+Romantic",
      popularity: 85,
      difficulty: "中等",
      estimatedTime: "3-4分钟",
      tags: ["森林", "自然", "梦幻", "绿色"],
      features: ["森林背景", "自然光线", "花朵装饰"]
    },
    {
      id: "vintage-classic",
      name: "复古经典",
      description: "永恒的复古婚纱风格，经典优雅",
      previewImage: "/placeholder.svg?height=400&width=300&text=Vintage+Classic",
      popularity: 82,
      difficulty: "中等",
      estimatedTime: "3-4分钟",
      tags: ["复古", "经典", "优雅", "怀旧"],
      features: ["复古造型", "珍珠装饰", "黑白色调"]
    },
    {
      id: "modern-chic",
      name: "现代时尚",
      description: "现代婚纱风格，简洁线条和时尚美学",
      previewImage: "/placeholder.svg?height=400&width=300&text=Modern+Chic",
      popularity: 90,
      difficulty: "简单",
      estimatedTime: "2-3分钟",
      tags: ["现代", "时尚", "简约", "都市"],
      features: ["简洁线条", "都市背景", "现代设计"]
    }
  ];

  useEffect(() => {
    // 获取上传的照片和元数据 - Get uploaded photo and metadata from sessionStorage
    const photo = sessionStorage.getItem("uploadedPhoto");
    const metadata = sessionStorage.getItem("photoMetadata");

    if (!photo) {
      // 如果没有照片则重定向到上传页面 - Redirect to upload if no photo
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/upload`);
      return;
    }

    setUploadedPhoto(photo);

    if (metadata) {
      try {
        setPhotoMetadata(JSON.parse(metadata));
      } catch (error) {
        console.error("Error parsing photo metadata:", error);
      }
    }
  }, [router]);

  // 风格切换函数 - Style toggle function
  const toggleStyle = useCallback((styleId: string) => {
    setSelectedStyles(prev => {
      const isSelected = prev.includes(styleId);
      if (isSelected) {
        return prev.filter(id => id !== styleId);
      } else {
        // 限制最多选择3个风格 - Limit to maximum 3 styles
        if (prev.length >= 3) {
          // 可以添加提示信息 - Could add notification here
          return prev;
        }
        return [...prev, styleId];
      }
    });
  }, []);

  // 获取风格的受欢迎程度颜色 - Get popularity color for style
  const getPopularityColor = (popularity: number) => {
    if (popularity >= 90) return "text-green-600 bg-green-100";
    if (popularity >= 80) return "text-blue-600 bg-blue-100";
    return "text-yellow-600 bg-yellow-100";
  };

  // 获取难度颜色 - Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "简单": return "text-green-600 bg-green-100";
      case "中等": return "text-yellow-600 bg-yellow-100";
      case "困难": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  // 检查用户积分 - Check user credits
  const checkUserCredits = useCallback(async () => {
    try {
      const credits = await hasEnoughCredits(userId, selectedStyles.length);
      setHasCredits(credits);
      return credits;
    } catch (error) {
      console.error('Error checking credits:', error);
      setHasCredits(false);
      return false;
    }
  }, [userId, selectedStyles.length]);

  // 生成婚纱照处理函数 - Generate wedding photos handler
  const handleGenerate = useCallback(async () => {
    if (selectedStyles.length === 0) {
      alert(t("selectAtLeast"));
      return;
    }

    // 检查是否选择了太多风格 - Check if too many styles selected
    if (selectedStyles.length > 3) {
      alert("最多只能选择3种风格，请重新选择");
      return;
    }

    // 检查用户积分 - Check user credits
    const hasValidCredits = await checkUserCredits();
    if (!hasValidCredits) {
      setShowSubscriptionModal(true);
      return;
    }

    console.log("🎯 Starting new generation, clearing old results");
    setIsGenerating(true);

    // 清除之前的生成结果以确保新的生成 - Clear previous generation results
    sessionStorage.removeItem("generatedPhotos");

    // 存储选择的风格到sessionStorage - Store selected styles in sessionStorage
    const styleData = {
      selectedStyles,
      styleDetails: enhancedStyles.filter(style => selectedStyles.includes(style.id)),
      selectionTime: new Date().toISOString(),
      photoMetadata
    };

    sessionStorage.setItem("selectedStyles", JSON.stringify(selectedStyles));
    sessionStorage.setItem("styleSelectionData", JSON.stringify(styleData));

    const currentLocale = window.location.pathname.split('/')[1];
    router.push(`/${currentLocale}/generating`);
  }, [selectedStyles, enhancedStyles, photoMetadata, t, router, checkUserCredits]);

  if (!uploadedPhoto) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-white to-pink-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
          <p className="text-gray-600 mb-4">加载中...</p>
        </div>
      </div>
    );
  }

  const currentLocale = window.location.pathname.split('/')[1];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white via-pink-25 to-rose-50 py-12">
      {/* 背景装饰 - Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-pink-200 rounded-full opacity-10 animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-rose-200 rounded-full opacity-10 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-pink-300 rounded-full opacity-10 animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* 顶部导航栏 - Top navigation bar */}
        <div className="flex items-center justify-between mb-8">
          <Link href={`/${currentLocale}/upload`} className="inline-flex items-center text-pink-500 hover:text-pink-600 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" /> {t("backToUpload")}
          </Link>

          {/* 积分显示 - Credits display */}
          <CreditDisplay
            userId={userId}
            onUpgradeClick={() => setShowSubscriptionModal(true)}
            showUpgradeButton={true}
          />
        </div>

        <div className="max-w-7xl mx-auto">
          {/* 页面标题 - Page header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full mr-4">
                <Palette className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                {t("title")}
              </h1>
            </div>
            <p className="text-gray-600 text-lg mb-2">{t("subtitle")}</p>
            <p className="text-gray-500">{t("description")}</p>

            {/* 选择限制提示 - Selection limit notice */}
            <div className="mt-4 flex flex-col sm:flex-row items-center justify-center gap-4">
              <div className="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200">
                <Sparkles className="h-4 w-4 mr-2" />
                最多可选择 3 种风格 ({selectedStyles.length}/3)
              </div>

              {/* 积分需求提示 - Credit requirement notice */}
              <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm border ${
                hasCredits
                  ? 'bg-green-50 text-green-700 border-green-200'
                  : 'bg-red-50 text-red-700 border-red-200'
              }`}>
                <Crown className="h-4 w-4 mr-2" />
                需要 {selectedStyles.length || 1} 积分
                {!hasCredits && <span className="ml-2 text-xs">(积分不足)</span>}
              </div>
            </div>
          </div>

          {/* 上传照片预览和信息 - Uploaded photo preview and info */}
          <div className="flex justify-center mb-8">
            <Card className="p-6 bg-white/80 backdrop-blur-sm shadow-lg border-0">
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <img
                    src={uploadedPhoto}
                    alt="Uploaded photo"
                    className="w-24 h-24 object-cover rounded-lg shadow-md border-2 border-white"
                  />
                  <div className="absolute -top-2 -right-2 bg-green-500 text-white rounded-full p-1">
                    <Check className="h-3 w-3" />
                  </div>
                </div>
                <div className="text-left">
                  <h3 className="font-semibold text-gray-800 mb-2">您的照片已准备就绪</h3>
                  {photoMetadata && (
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>文件大小: {(photoMetadata.fileSize / 1024 / 1024).toFixed(2)} MB</p>
                      <p>照片质量: {
                        photoMetadata.quality === 'excellent' ? '优秀' :
                        photoMetadata.quality === 'good' ? '良好' :
                        photoMetadata.quality === 'fair' ? '一般' : '较差'
                      }</p>
                    </div>
                  )}
                  <div className="flex items-center mt-2">
                    <Sparkles className="h-4 w-4 text-pink-500 mr-1" />
                    <span className="text-sm text-pink-600">准备生成婚纱照</span>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* 风格选择网格 - Style selection grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {enhancedStyles.map((style) => {
              const isSelected = selectedStyles.includes(style.id);
              const isHovered = hoveredStyle === style.id;
              const canSelect = selectedStyles.length < 3 || isSelected;

              return (
                <Card
                  key={style.id}
                  className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                    isSelected
                      ? "ring-2 ring-pink-500 bg-pink-50 shadow-xl"
                      : canSelect
                      ? "hover:shadow-xl bg-white/80 backdrop-blur-sm"
                      : "opacity-50 cursor-not-allowed bg-gray-50"
                  } ${!canSelect ? "pointer-events-none" : ""}`}
                  onClick={() => canSelect && toggleStyle(style.id)}
                  onMouseEnter={() => setHoveredStyle(style.id)}
                  onMouseLeave={() => setHoveredStyle(null)}
                >
                  <div className="relative overflow-hidden">
                    {/* 风格预览图片 - Style preview image */}
                    <div className="relative h-48 bg-gradient-to-br from-pink-100 to-rose-100 overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <Camera className="h-12 w-12 text-pink-400 mx-auto mb-2" />
                          <span className="text-pink-600 font-medium">{style.name}</span>
                        </div>
                      </div>

                      {/* 悬停效果 - Hover effect */}
                      {isHovered && (
                        <div className="absolute inset-0 bg-pink-500/20 flex items-center justify-center">
                          <div className="bg-white/90 rounded-full p-3">
                            <Eye className="h-6 w-6 text-pink-600" />
                          </div>
                        </div>
                      )}

                      {/* 选中标记 - Selected indicator */}
                      {isSelected && (
                        <div className="absolute top-4 right-4 bg-pink-500 text-white rounded-full p-2 shadow-lg">
                          <Check className="h-4 w-4" />
                        </div>
                      )}

                      {/* 受欢迎程度标记 - Popularity badge */}
                      <div className="absolute top-4 left-4">
                        <Badge className={`${getPopularityColor(style.popularity)} border-0`}>
                          <Star className="h-3 w-3 mr-1" />
                          {style.popularity}%
                        </Badge>
                      </div>
                    </div>

                    {/* 风格信息 - Style information */}
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <h3 className="text-lg font-semibold text-gray-800">{style.name}</h3>
                        <Badge className={`${getDifficultyColor(style.difficulty)} border-0 text-xs`}>
                          {style.difficulty}
                        </Badge>
                      </div>

                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{style.description}</p>

                      {/* 标签 - Tags */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {style.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-pink-100 text-pink-700 rounded-full text-xs"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>

                      {/* 特性列表 - Features list */}
                      <div className="space-y-1 mb-4">
                        {style.features.slice(0, 2).map((feature, index) => (
                          <div key={index} className="flex items-center text-xs text-gray-500">
                            <Sparkles className="h-3 w-3 mr-2 text-pink-400" />
                            {feature}
                          </div>
                        ))}
                      </div>

                      {/* 预估时间 - Estimated time */}
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">预估时间:</span>
                        <span className="font-medium text-pink-600">{style.estimatedTime}</span>
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>

          {/* 选中风格摘要 - Selected styles summary */}
          {selectedStyles.length > 0 && (
            <Card className="p-6 mb-8 bg-gradient-to-r from-pink-50 to-rose-50 border-pink-200 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-pink-800 flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  已选择的风格 ({selectedStyles.length}/3)
                </h3>
                {selectedStyles.length === 3 && (
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    <Check className="h-3 w-3 mr-1" />
                    已达上限
                  </Badge>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {selectedStyles.map((styleId) => {
                  const style = enhancedStyles.find(s => s.id === styleId);
                  if (!style) return null;

                  return (
                    <div
                      key={styleId}
                      className="bg-white rounded-lg p-4 border border-pink-200 shadow-sm"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-800">{style.name}</h4>
                        <button
                          onClick={() => toggleStyle(styleId)}
                          className="text-gray-400 hover:text-red-500 transition-colors"
                        >
                          ×
                        </button>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{style.description}</p>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-pink-600">预估: {style.estimatedTime}</span>
                        <Badge className={`${getDifficultyColor(style.difficulty)} border-0`}>
                          {style.difficulty}
                        </Badge>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* 预估总时间 - Estimated total time */}
              <div className="mt-4 p-3 bg-white rounded-lg border border-pink-200">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">预估总生成时间:</span>
                  <span className="font-semibold text-pink-600">
                    {selectedStyles.length * 3} - {selectedStyles.length * 4} 分钟
                  </span>
                </div>
              </div>
            </Card>
          )}

          {/* 生成按钮 - Generate button */}
          <div className="text-center space-y-4">
            <Button
              className={`bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-12 py-6 rounded-xl text-lg font-medium min-w-[250px] transition-all duration-300 transform hover:scale-105 disabled:transform-none disabled:opacity-50 ${
                isGenerating ? 'animate-pulse' : ''
              }`}
              disabled={selectedStyles.length === 0 || isGenerating || !hasCredits}
              onClick={handleGenerate}
            >
              {isGenerating ? (
                <div className="flex items-center justify-center">
                  <Sparkles className="h-5 w-5 mr-2 animate-spin" />
                  准备生成中...
                </div>
              ) : !hasCredits ? (
                <div className="flex items-center justify-center">
                  <Crown className="h-5 w-5 mr-2" />
                  升级订阅以生成
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <Sparkles className="h-5 w-5 mr-2" />
                  {t("generatePhotos")} ({selectedStyles.length} 种风格)
                </div>
              )}
            </Button>

            {selectedStyles.length === 0 && (
              <p className="text-sm text-gray-500">请至少选择一种风格开始生成</p>
            )}

            {/* 积分不足提示 - Insufficient credits notice */}
            {!hasCredits && selectedStyles.length > 0 && (
              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-yellow-800 font-medium">积分不足</p>
                    <p className="text-yellow-700 text-sm mt-1">
                      您需要 {selectedStyles.length} 个积分来生成选中的风格。请升级您的订阅计划以获得更多积分。
                    </p>
                    <Button
                      size="sm"
                      className="mt-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                      onClick={() => setShowSubscriptionModal(true)}
                    >
                      <Crown className="h-4 w-4 mr-2" />
                      查看订阅计划
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 使用提示 - Usage tips */}
          <div className="mt-12">
            <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <h3 className="font-semibold text-blue-800 mb-3 flex items-center">
                <Sparkles className="h-5 w-5 mr-2" />
                生成小贴士
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                <div className="flex items-start">
                  <Check className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>建议选择2-3种不同风格以获得多样化效果</span>
                </div>
                <div className="flex items-start">
                  <Check className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>每种风格都会生成独特的婚纱照</span>
                </div>
                <div className="flex items-start">
                  <Check className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>生成过程中请保持页面打开</span>
                </div>
                <div className="flex items-start">
                  <Check className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>生成完成后可立即下载和分享</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* 订阅模态框 - Subscription modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        userId={userId}
        onSubscriptionChange={() => {
          // 重新检查积分状态 - Recheck credit status
          checkUserCredits();
        }}
        requiredCredits={selectedStyles.length || 1}
      />
    </div>
  );
}
