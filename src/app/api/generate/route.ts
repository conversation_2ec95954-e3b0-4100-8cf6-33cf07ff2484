import { NextRequest, NextResponse } from "next/server";
import OpenA<PERSON> from "openai";
import { createHenAPIService } from "@/lib/henapi";
import { createAPICoreService } from "@/lib/apicore";
import { quota<PERSON>anager, QUOTA_CONSTANTS, getQuotaStatus, QuotaStatus } from "@/lib/quota-manager";
import { generateMockWeddingPhotos, shouldUseMockGeneration } from "@/lib/mock-generation";
import { hasEnoughCredits, consumeCredits } from "@/lib/subscription";

// Real API endpoint for generating wedding photos using OpenAI or HenAPI
// Supports multiple AI image generation providers with configurable mode switching

// Get AI generation mode from environment
const AI_GENERATION_MODE = process.env.AI_GENERATION_MODE || "openai";

// Initialize OpenAI client conditionally (only for openai mode)
let openai: OpenAI | null = null;
if (AI_GENERATION_MODE === "openai") {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL || "https://api.laozhang.ai/v1"
  });
}

// Initialize HenAPI service (for henapi mode)
const henApiService = createHenAPIService();

// Initialize APICore service (for apicore mode)
const apicoreService = createAPICoreService();

export async function POST(request: NextRequest) {
  const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const debugMode = process.env.NODE_ENV === 'development' || process.env.DEBUG_MODE === 'true';

  try {
    console.log(`🚀 [${requestId}] Starting wedding photo generation API call`);

    if (debugMode) {
      console.log(`🔧 [${requestId}] DEBUG MODE ENABLED - Detailed logging active`);
    }

    console.log(`🔧 [${requestId}] Environment:`, {
      NODE_ENV: process.env.NODE_ENV,
      DEBUG_MODE: process.env.DEBUG_MODE,
      USE_PLACEHOLDER_IMAGES: process.env.USE_PLACEHOLDER_IMAGES,
      AI_GENERATION_MODE,
      hasOpenAIKey: !!process.env.OPENAI_API_KEY,
      hasHenAPIKey: !!process.env.HENAPI_API_KEY,
      hasAPICoreKey: !!process.env.APICORE_API_KEY,
      openaiBaseURL: process.env.OPENAI_BASE_URL || "https://api.laozhang.ai/v1",
      henApiBaseURL: process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1",
      apicoreBaseURL: process.env.APICORE_BASE_URL || "https://api.apicore.ai/v1"
    });

    // 详细的配额状态日志
    if (AI_GENERATION_MODE === "henapi" && debugMode) {
      const quotaStatus = quotaManager.getQuotaStatus();
      const usageStats = quotaManager.getUsageStats();

      console.log(`💰 [${requestId}] DETAILED QUOTA STATUS:`, {
        currentQuota: quotaStatus.current,
        lastUpdated: quotaStatus.lastUpdated.toISOString(),
        fallbackRecommended: quotaStatus.fallbackRecommended,
        recentUsageCount: quotaStatus.recentUsage.length,
        totalOperations: usageStats.totalOperations,
        successRate: `${usageStats.successRate.toFixed(1)}%`,
        fallbackRate: `${usageStats.fallbackRate.toFixed(1)}%`
      });

      // 显示最近的使用记录
      if (quotaStatus.recentUsage.length > 0) {
        console.log(`📋 [${requestId}] RECENT QUOTA USAGE:`);
        quotaStatus.recentUsage.forEach((record, index) => {
          console.log(`   ${index + 1}. ${record.timestamp} - ${record.operation} - ${record.success ? 'SUCCESS' : 'FAILED'} - Remaining: ${record.quotaRemaining}${record.fallbackUsed ? ' (FALLBACK)' : ''}`);
        });
      }
    }

    // Check API configuration based on selected mode
    if (AI_GENERATION_MODE === "henapi") {
      if (!process.env.HENAPI_API_KEY) {
        console.error(`❌ [${requestId}] HENAPI_API_KEY not configured for henapi mode`);
        return NextResponse.json({
          error: "API configuration error: Missing HenAPI API key for henapi mode",
          requestId
        }, { status: 500 });
      }
      if (!henApiService) {
        console.error(`❌ [${requestId}] HenAPI service initialization failed`);
        return NextResponse.json({
          error: "API configuration error: HenAPI service initialization failed",
          requestId
        }, { status: 500 });
      }
    } else if (AI_GENERATION_MODE === "apicore") {
      if (!process.env.APICORE_API_KEY) {
        console.error(`❌ [${requestId}] APICORE_API_KEY not configured for apicore mode`);
        return NextResponse.json({
          error: "API configuration error: Missing APICore API key for apicore mode",
          requestId
        }, { status: 500 });
      }
      if (!apicoreService) {
        console.error(`❌ [${requestId}] APICore service initialization failed`);
        return NextResponse.json({
          error: "API configuration error: APICore service initialization failed",
          requestId
        }, { status: 500 });
      }
    } else {
      // Default to OpenAI mode
      if (!process.env.OPENAI_API_KEY) {
        console.error(`❌ [${requestId}] OPENAI_API_KEY not configured for openai mode`);
        return NextResponse.json({
          error: "API configuration error: Missing OpenAI API key for openai mode",
          requestId
        }, { status: 500 });
      }
    }

    // Parse the request body first - 首先解析请求体
    const body = await request.json();
    const { photoUrl, styles, userId } = body;

    console.log(`📝 [${requestId}] Request data:`, {
      photoUrl: photoUrl ? `${photoUrl.substring(0, 50)}...` : "missing",
      styles,
      stylesCount: Array.isArray(styles) ? styles.length : 0,
      userId,
      bodyKeys: Object.keys(body)
    });

    // 开发模式检查 - 使用模拟生成服务 - Development mode check - use mock generation service
    const useMockGeneration = shouldUseMockGeneration();
    console.log(`🔧 [${requestId}] Mock generation mode:`, useMockGeneration);

    // 如果使用模拟生成，直接返回模拟数据 - If using mock generation, return mock data directly
    if (useMockGeneration) {
      console.log(`🎭 [${requestId}] Using mock generation service for development`);

      try {
        const mockPhotos = await generateMockWeddingPhotos(photoUrl, styles, userId);

        const responseData = {
          success: true,
          jobId: `mock-job-${Date.now()}`,
          photos: mockPhotos,
          estimatedTime: "2-3 minutes",
          status: "completed",
          requestId,
          mockGeneration: true,
          summary: {
            total: mockPhotos.length,
            successful: mockPhotos.length,
            failed: 0
          }
        };

        console.log(`🎉 [${requestId}] Mock generation completed successfully with ${mockPhotos.length} photos`);
        return NextResponse.json(responseData);

      } catch (error) {
        console.error(`❌ [${requestId}] Mock generation failed:`, error);
        return NextResponse.json({
          error: "Mock generation failed",
          details: error instanceof Error ? error.message : "Unknown error",
          requestId
        }, { status: 500 });
      }
    }

    // 生产模式继续使用真实API - Production mode continues with real API
    const isDevelopmentMode = process.env.NODE_ENV === 'development' && process.env.USE_PLACEHOLDER_IMAGES === 'true';
    console.log(`🔧 [${requestId}] Development mode (legacy):`, isDevelopmentMode);



    // 检查用户积分 - Check user credits (only for real generation, not mock)
    if (userId && !useMockGeneration) {
      const requiredCredits = Array.isArray(styles) ? styles.length : 1;
      const hasCredits = await hasEnoughCredits(userId, requiredCredits);

      if (!hasCredits) {
        console.log(`❌ [${requestId}] Insufficient credits for user: ${userId}, required: ${requiredCredits}`);
        return NextResponse.json({
          error: "Insufficient credits",
          message: "您的积分不足，请升级订阅计划",
          messageEn: "Insufficient credits, please upgrade your subscription plan",
          requiredCredits,
          requestId
        }, { status: 402 }); // 402 Payment Required
      }

      console.log(`✅ [${requestId}] User has sufficient credits for ${requiredCredits} generations`);
    }

    if (!photoUrl) {
      console.error(`❌ [${requestId}] Error: Photo URL is required`);
      return NextResponse.json({
        error: "Photo URL is required",
        requestId
      }, { status: 400 });
    }

    if (!styles || !Array.isArray(styles) || styles.length === 0) {
      console.error(`❌ [${requestId}] Error: At least one style must be selected. Received:`, styles);
      return NextResponse.json({
        error: "At least one style must be selected",
        received: styles,
        requestId
      }, { status: 400 });
    }

    // Validate styles
    const validStyles = [
      "chinese-traditional",
      "western-elegant",
      "beach-sunset",
      "forest-romantic",
      "vintage-classic",
      "modern-chic"
    ];

    const invalidStyles = styles.filter(style => !validStyles.includes(style));
    if (invalidStyles.length > 0) {
      console.error(`❌ [${requestId}] Error: Invalid styles:`, invalidStyles);
      return NextResponse.json({
        error: `Invalid styles: ${invalidStyles.join(", ")}`,
        invalidStyles,
        validStyles,
        requestId
      }, { status: 400 });
    }

    console.log(`✅ [${requestId}] Validation passed, proceeding with generation for ${styles.length} styles`);

    // 配额预检查 - 避免不必要的API调用
    if (AI_GENERATION_MODE === "henapi") {
      const quotaStatus = quotaManager.getQuotaStatus();
      const requiredQuota = QUOTA_CONSTANTS.FACE_SWAP_COST;
      const standardQuota = QUOTA_CONSTANTS.STANDARD_COST;

      console.log(`💰 [${requestId}] Quota pre-check:`, {
        current: quotaStatus.current,
        required: requiredQuota,
        standardRequired: standardQuota,
        sufficient: quotaStatus.current >= requiredQuota,
        canUseStandard: quotaStatus.current >= standardQuota,
        shortfall: Math.max(0, requiredQuota - quotaStatus.current),
        lastUpdated: quotaStatus.lastUpdated.toISOString()
      });

      if (debugMode) {
        console.log(`🔍 [${requestId}] QUOTA ANALYSIS:`, {
          faceSwapPossible: quotaStatus.current >= requiredQuota,
          standardGenerationPossible: quotaStatus.current >= standardQuota,
          recommendedAction: quotaStatus.current >= requiredQuota ? 'FACE_SWAP' :
                           quotaStatus.current >= standardQuota ? 'STANDARD_GENERATION' :
                           'INSUFFICIENT_QUOTA',
          quotaUtilization: `${((quotaStatus.current / requiredQuota) * 100).toFixed(1)}%`,
          fallbackRecommended: quotaStatus.fallbackRecommended
        });
      }

      if (quotaStatus.current < requiredQuota) {
        console.log(`⚠️ [${requestId}] Insufficient quota for face swap detected`);

        if (quotaStatus.current >= standardQuota) {
          console.log(`💡 [${requestId}] Standard generation is possible, but face swap is not`);
          console.log(`📊 [${requestId}] Quota breakdown: Current=${quotaStatus.current}, FaceSwap=${requiredQuota}, Standard=${standardQuota}`);
        } else {
          console.log(`❌ [${requestId}] Insufficient quota for any generation mode`);
          console.log(`📊 [${requestId}] Quota breakdown: Current=${quotaStatus.current}, Minimum=${standardQuota}`);

          // 生成详细的配额报告
          if (debugMode) {
            console.log(`📋 [${requestId}] QUOTA REPORT:`);
            console.log(quotaManager.generateReport());
          }

          return NextResponse.json({
            success: false,
            error: "insufficient_quota",
            message: "配额不足，无法生成婚纱照",
            quotaInfo: {
              current: quotaStatus.current,
              required: requiredQuota,
              standardRequired: standardQuota,
              shortfall: requiredQuota - quotaStatus.current,
              percentage: (quotaStatus.current / requiredQuota * 100).toFixed(1),
              lastUpdated: quotaStatus.lastUpdated.toISOString(),
              canUseStandard: quotaStatus.current >= standardQuota
            },
            recommendations: [
              "为HenAPI账户充值配额",
              `需要补充 ${requiredQuota - quotaStatus.current} 配额用于人脸替换`,
              quotaStatus.current >= standardQuota ? "可以使用标准生成模式" : `需要至少 ${standardQuota - quotaStatus.current} 配额用于标准生成`,
              "联系管理员获取更多配额",
              "稍后重试或切换到其他生成模式"
            ],
            debugInfo: debugMode ? {
              usageStats: quotaManager.getUsageStats(),
              recentUsage: quotaStatus.recentUsage
            } : undefined,
            requestId
          }, { status: 402 }); // 402 Payment Required
        }
      } else {
        console.log(`✅ [${requestId}] Sufficient quota available for face swap generation`);
      }
    }

    // Validate photo format - support both data URLs and HTTP URLs
    let imageFormat: string;
    let isDataUrl = false;

    if (photoUrl.startsWith('data:image/')) {
      // Data URL format: data:image/jpeg;base64,/9j/4AAQ...
      isDataUrl = true;
      imageFormat = photoUrl.split(';')[0].split('/')[1]?.toLowerCase();
    } else if (photoUrl.startsWith('http://') || photoUrl.startsWith('https://')) {
      // HTTP URL format: https://example.com/image.jpg
      const urlPath = photoUrl.split('?')[0]; // Remove query parameters
      const pathParts = urlPath.split('/');
      const filename = pathParts[pathParts.length - 1];
      const extension = filename.includes('.') ? filename.split('.').pop()?.toLowerCase() : '';

      // For URLs without clear extensions, assume jpeg (common for dynamic image URLs)
      if (!extension || !['jpeg', 'jpg', 'png', 'gif', 'webp'].includes(extension)) {
        imageFormat = 'jpeg'; // Default assumption for HTTP URLs
        console.log(`🔍 [${requestId}] No clear extension found in URL, assuming JPEG format`);
      } else {
        imageFormat = extension === 'jpg' ? 'jpeg' : extension;
      }
    } else {
      console.error(`❌ [${requestId}] Invalid photo format. Expected data URL or HTTP URL, got:`, photoUrl.substring(0, 50));
      return NextResponse.json({
        error: "Invalid photo format. Please provide a data URL (data:image/...) or HTTP URL (https://...).",
        requestId
      }, { status: 400 });
    }

    // Check if the image format is supported
    const supportedFormats = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
    console.log(`🔍 [${requestId}] Detected image format:`, imageFormat, `(${isDataUrl ? 'data URL' : 'HTTP URL'})`);

    if (!supportedFormats.includes(imageFormat)) {
      console.error(`❌ [${requestId}] Unsupported image format: ${imageFormat}. Supported formats:`, supportedFormats);
      return NextResponse.json({
        error: `Unsupported image format: ${imageFormat}. Please upload a JPEG, PNG, GIF, or WebP image.`,
        supportedFormats,
        requestId
      }, { status: 400 });
    }

    // First, analyze the uploaded photo using OpenAI Vision API (only in OpenAI mode)
    let photoAnalysis = "";
    let visionApiUsed = false;

    // Only use Vision API if we're in OpenAI mode and have the client
    if (AI_GENERATION_MODE === "openai" && openai) {
      try {
        console.log(`🔍 [${requestId}] Analyzing uploaded photo with Vision API...`);
        console.log(`📷 [${requestId}] Photo details:`, {
          length: photoUrl.length,
          format: imageFormat,
          preview: photoUrl.substring(0, 50) + "..."
        });

        const visionResponse = await openai.chat.completions.create({
        model: "dall-e-3", // Use dall-e-3 model for better quality
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Analyze this photo and describe the person's appearance, facial features, hair style, skin tone, and any distinctive characteristics. Focus on details that would help recreate a similar-looking person in wedding photography. Be specific but respectful."
              },
              {
                type: "image_url",
                image_url: {
                  url: photoUrl
                }
              }
            ]
          }
        ],
        max_tokens: 300
      });

      photoAnalysis = visionResponse.choices[0]?.message?.content || "";
      visionApiUsed = true;
      console.log(`✅ [${requestId}] Photo analysis completed successfully`);
      console.log(`📝 [${requestId}] Analysis result:`, photoAnalysis.substring(0, 150) + "...");

    } catch (error) {
      console.error(`⚠️ [${requestId}] Vision API failed, using generic prompts`);
      console.error(`❌ [${requestId}] Vision API error details:`, {
        message: error instanceof Error ? error.message : "Unknown error",
        name: error instanceof Error ? error.name : "Unknown",
        status: (error as any)?.status || "Unknown",
        code: (error as any)?.code || "Unknown",
        type: (error as any)?.type || "Unknown"
      });

      // Provide different fallback descriptions based on image format
      if (imageFormat === 'svg') {
        photoAnalysis = "Beautiful bride with artistic illustration style, elegant features and graceful appearance";
      } else {
        photoAnalysis = "Beautiful bride with elegant features, radiant smile, and graceful appearance";
      }

      console.log(`🔄 [${requestId}] Using fallback description:`, photoAnalysis);
    }
  } else {
    // Not in OpenAI mode, skip Vision API and use generic description
    console.log(`🔧 [${requestId}] Skipping Vision API (mode: ${AI_GENERATION_MODE}), using generic description`);
    photoAnalysis = "Beautiful bride with elegant features, radiant smile, and graceful appearance";
  }

    // Style descriptions for wedding photo prompts - optimized for face swap and style transfer
    const stylePrompts: Record<string, string> = {
      "chinese-traditional": "Transform into a beautiful bride wearing elegant traditional Chinese qipao wedding dress, luxurious red and gold silk fabric with intricate dragon and phoenix embroidery, ornate traditional patterns, classical Chinese garden with ancient pavilion and blooming cherry blossoms, maintain original facial features and expression, professional wedding photography with soft lighting, high resolution, photorealistic",
      "western-elegant": "Transform into a stunning bride in flowing white wedding gown with delicate lace details and cathedral train, holding white roses bouquet, romantic church interior with magnificent stained glass windows, preserve natural facial characteristics, radiant bridal smile, classic western bridal portrait photography, soft romantic lighting, ultra-high quality",
      "beach-sunset": "Transform into a gorgeous bride in flowing bohemian beach wedding dress, soft chiffon fabric flowing in ocean breeze, barefoot on pristine sandy beach, golden hour sunset over crystal blue ocean waves, warm romantic golden lighting, maintain facial identity, joyful natural expression, destination wedding photography, dreamy atmosphere",
      "forest-romantic": "Transform into an enchanting bride in romantic A-line wedding dress with delicate floral lace, magical woodland fairy-tale setting, dappled sunlight filtering through ancient trees, wildflowers and lush greenery, preserve original face and features, dreamy romantic smile, outdoor wedding photography, ethereal forest atmosphere",
      "vintage-classic": "Transform into an elegant bride in vintage 1950s style wedding dress, classic silhouette with pearl details and vintage accessories, retro hairstyle with delicate veil, maintain facial identity, timeless black and white photography aesthetic with classic pose, vintage wedding portrait, sophisticated lighting",
      "modern-chic": "Transform into a sophisticated bride in sleek modern wedding dress, minimalist design with clean architectural lines, contemporary urban setting or modern venue with geometric elements, preserve natural facial features, confident modern smile, high-fashion wedding photography, crisp professional lighting"
    };

    const styleNames: Record<string, string> = {
      "chinese-traditional": "Chinese Traditional",
      "western-elegant": "Western Elegant",
      "beach-sunset": "Beach Sunset",
      "forest-romantic": "Forest Romantic",
      "vintage-classic": "Vintage Classic",
      "modern-chic": "Modern Chic"
    };

    // Generate photos using OpenAI API
    console.log(`🎨 [${requestId}] Starting AI image generation for ${styles.length} styles:`, styles);
    const generatedPhotos = [];

    for (let i = 0; i < styles.length; i++) {
      const style = styles[i];
      const stylePrompt = stylePrompts[style];

      console.log(`🖼️ [${requestId}] Generating image ${i + 1}/${styles.length} for style: ${style}`);

      if (!stylePrompt) {
        console.error(`❌ [${requestId}] No prompt found for style: ${style}`);
        continue;
      }

      try {
        let imageUrl: string;
        let useFaceSwap = true; // 默认尝试人脸替换

        if (isDevelopmentMode) {
          // Development mode: create high-quality placeholder
          console.log(`🔧 [${requestId}] Development mode: Using placeholder image for style: ${style}`);

          imageUrl = `data:image/svg+xml;base64,${Buffer.from(`
            <svg width="1024" height="1024" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#fdf2f8;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#fce7f3;stop-opacity:1" />
                </linearGradient>
              </defs>
              <rect width="100%" height="100%" fill="url(#bg)"/>
              <circle cx="512" cy="400" r="150" fill="#f9a8d4" opacity="0.3"/>
              <text x="50%" y="30%" text-anchor="middle" dy=".3em" font-family="serif" font-size="36" fill="#be185d" font-weight="bold">
                ${styleNames[style] || style}
              </text>
              <text x="50%" y="40%" text-anchor="middle" dy=".3em" font-family="serif" font-size="24" fill="#9d174d">
                Wedding Photography
              </text>
              <text x="50%" y="70%" text-anchor="middle" dy=".3em" font-family="sans-serif" font-size="16" fill="#831843">
                Development Mode
              </text>
              <text x="50%" y="75%" text-anchor="middle" dy=".3em" font-family="sans-serif" font-size="14" fill="#9f1239">
                High-quality placeholder image
              </text>
            </svg>
          `).toString('base64')}`;

        } else {
          // Production mode: call real API with enhanced prompts for face swap
          let prompt: string;

          if (AI_GENERATION_MODE === "henapi") {
            // HenAPI模式：优化的人脸替换提示词
            const faceSwapPrompt = stylePrompt; // 使用优化后的风格描述
            const qualityEnhancement = "Ultra-high resolution, professional photography, perfect lighting, photorealistic, maintain facial identity and natural expression, seamless face integration, high-quality wedding portrait";
            const facePreservation = photoAnalysis ?
              `Preserve the person's distinctive features: ${photoAnalysis}. Keep natural facial characteristics while applying the wedding style.` :
              "Preserve the person's natural facial features and expression while transforming into the wedding style.";

            prompt = `${faceSwapPrompt} ${facePreservation} ${qualityEnhancement}`;
          } else {
            // OpenAI模式：传统的描述性提示词
            const basePrompt = `Professional wedding photography: ${stylePrompt}`;
            const personDescription = photoAnalysis ? `The bride has the following characteristics: ${photoAnalysis}.` : "Beautiful bride with elegant features.";
            const qualityAndPose = "Ultra-high quality, 8K resolution, perfect lighting, romantic and elegant atmosphere. The bride must be facing directly towards the camera, front-facing portrait, beautiful makeup and hairstyle, radiant smile, professional bridal pose. Photorealistic, magazine-quality wedding photography, no text or watermarks.";

            prompt = `${basePrompt} ${personDescription} ${qualityAndPose}`;
          }

          console.log(`📝 [${requestId}] Using enhanced prompt for ${style}:`, prompt.substring(0, 200) + "...");
          console.log(`🎯 [${requestId}] Photo analysis status:`, {
            visionApiUsed,
            hasAnalysis: !!photoAnalysis,
            analysisLength: photoAnalysis.length,
            imageFormat
          });

          const apiParams = {
            model: "dall-e-3", // Use dall-e-3 model for better quality
            prompt: prompt,
            n: 1,
            size: "1024x1024" as const,
            quality: "standard" as const,
          };

          console.log(`🔄 [${requestId}] Calling ${AI_GENERATION_MODE.toUpperCase()} API with params:`, {
            mode: AI_GENERATION_MODE,
            model: apiParams.model,
            promptLength: apiParams.prompt.length,
            n: apiParams.n,
            size: apiParams.size,
            quality: apiParams.quality
          });

          let apiImageUrl: string;

          if (AI_GENERATION_MODE === "henapi" && henApiService) {
            // Call HenAPI for image generation
            console.log(`🔄 [${requestId}] Using HenAPI for image generation`);
            console.log(`🔧 [${requestId}] HenAPI Service Configuration:`, {
              hasService: !!henApiService,
              baseURL: process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1",
              hasApiKey: !!process.env.HENAPI_API_KEY,
              apiKeyPreview: process.env.HENAPI_API_KEY ? `${process.env.HENAPI_API_KEY.substring(0, 8)}...${process.env.HENAPI_API_KEY.slice(-4)}` : 'NOT_SET'
            });

            // 智能配额管理：先尝试人脸替换，失败时回退到普通生成
            let henApiParams: any;

            // 首先尝试人脸替换模式
            henApiParams = {
              model: apiParams.model,
              prompt: apiParams.prompt,
              n: apiParams.n,
              size: apiParams.size,
              // 添加用户上传的图片用于人脸替换
              image_url: photoUrl, // 使用用户上传的图片作为参考
              face_swap: true, // 启用人脸替换功能
              style_transfer: true, // 启用风格转换
              strength: 0.8, // 生成强度，保持较高的相似度
              guidance_scale: 7.5 // 引导比例，平衡质量和创意
            };

            console.log(`📤 [${requestId}] Calling HenAPI with face swap parameters:`, {
              model: henApiParams.model,
              promptLength: henApiParams.prompt.length,
              promptPreview: henApiParams.prompt.substring(0, 150) + "...",
              n: henApiParams.n,
              size: henApiParams.size,
              hasImageUrl: !!henApiParams.image_url,
              imageUrlPreview: henApiParams.image_url ? `${henApiParams.image_url.substring(0, 80)}...` : 'N/A',
              faceSwap: henApiParams.face_swap,
              styleTransfer: henApiParams.style_transfer,
              strength: henApiParams.strength,
              guidanceScale: henApiParams.guidance_scale
            });

            let henResponse;
            try {
              // 尝试人脸替换模式
              henResponse = await henApiService.generateImage(henApiParams);
              console.log(`✅ [${requestId}] Face swap generation successful`);
            } catch (error) {
              // 检查是否是配额不足错误
              if (error instanceof Error && error.message.includes('insufficient_user_quota')) {
                console.log(`⚠️ [${requestId}] Face swap quota insufficient, falling back to standard generation`);

                // 解析配额信息并更新配额管理器
                const quotaInfo = quotaManager.parseQuotaError(error.message);
                if (quotaInfo) {
                  quotaManager.updateQuota(quotaInfo.current);
                  console.log(`📊 [${requestId}] Quota status: ${quotaInfo.current}/${quotaInfo.required} (${quotaInfo.percentage.toFixed(1)}%)`);
                }

                // 记录失败的人脸替换尝试
                quotaManager.recordUsage('face_swap', QUOTA_CONSTANTS.FACE_SWAP_COST, quotaInfo?.current || 0, false);

                // 回退到普通生成模式
                useFaceSwap = false;
                henApiParams = {
                  model: apiParams.model,
                  prompt: apiParams.prompt,
                  n: apiParams.n,
                  size: apiParams.size
                  // 移除人脸替换相关参数
                };

                console.log(`🔄 [${requestId}] Retrying with standard generation parameters`);
                henResponse = await henApiService.generateImage(henApiParams);
                console.log(`✅ [${requestId}] Standard generation successful (fallback mode)`);

                // 记录成功的标准生成
                quotaManager.recordUsage('standard_generation', QUOTA_CONSTANTS.STANDARD_COST, quotaInfo?.current || 0, true, true);
              } else {
                // 其他错误，直接抛出
                throw error;
              }
            }

            console.log(`📥 [${requestId}] HenAPI response for ${style}:`, {
              hasData: !!henResponse.data,
              dataLength: henResponse.data?.length || 0,
              firstImageHasUrl: !!henResponse.data?.[0]?.url,
              responseKeys: Object.keys(henResponse),
              dataKeys: henResponse.data?.[0] ? Object.keys(henResponse.data[0]) : []
            });

            const henImageUrl = henResponse.data?.[0]?.url;
            if (!henImageUrl) {
              console.error(`❌ [${requestId}] No image URL returned from HenAPI for style: ${style}`);
              console.error(`❌ [${requestId}] Full response structure:`, {
                hasData: !!henResponse.data,
                dataLength: henResponse.data?.length || 0,
                firstItem: henResponse.data?.[0] || null,
                fullResponse: henResponse
              });
              throw new Error("No image URL returned from HenAPI");
            }

            console.log(`✅ [${requestId}] Successfully generated image via HenAPI for style: ${style}`);
            console.log(`🔗 [${requestId}] Image URL details:`, {
              urlLength: henImageUrl.length,
              urlPreview: henImageUrl.substring(0, 100) + "...",
              isValidUrl: henImageUrl.startsWith('http')
            });
            apiImageUrl = henImageUrl;
          } else if (AI_GENERATION_MODE === "openai" && openai) {
            // Call OpenAI API for image generation (default mode)
            console.log(`🔄 [${requestId}] Using OpenAI for image generation`);
            const response = await openai.images.generate(apiParams);

            console.log(`📥 [${requestId}] OpenAI API response for ${style}:`, {
              hasData: !!response.data,
              dataLength: response.data?.length || 0,
              firstImageHasUrl: !!response.data?.[0]?.url
            });

            const openaiImageUrl = response.data?.[0]?.url;
            if (!openaiImageUrl) {
              console.error(`❌ [${requestId}] No image URL returned from OpenAI for style: ${style}`);
              console.error(`❌ [${requestId}] Full response:`, response);
              throw new Error("No image URL returned from OpenAI");
            }

            console.log(`✅ [${requestId}] Successfully generated image via OpenAI for style: ${style}, URL length: ${openaiImageUrl.length}`);
            apiImageUrl = openaiImageUrl;
          } else {
            // Neither API is properly configured
            console.error(`❌ [${requestId}] No valid API configuration found for mode: ${AI_GENERATION_MODE}`);
            throw new Error(`No valid API configuration found for mode: ${AI_GENERATION_MODE}. Please check your environment variables.`);
          }

          imageUrl = apiImageUrl;
        }

        // 添加生成模式标记
        const photoData: any = {
          id: `photo-${Date.now()}-${i}`,
          style: style,
          styleName: styleNames[style] || style,
          imageUrl: imageUrl,
          originalPhoto: photoUrl,
          createdAt: new Date().toISOString(),
          userId: userId || null,
        };

        // 如果是HenAPI模式，添加生成模式信息
        if (AI_GENERATION_MODE === "henapi") {
          photoData.generationMode = useFaceSwap ? "face_swap" : "standard";
          photoData.faceSwapEnabled = useFaceSwap;
          if (!useFaceSwap) {
            photoData.fallbackReason = "quota_insufficient";
            photoData.note = "Generated using standard mode due to insufficient quota for face swap";
          }
        }

        generatedPhotos.push(photoData);

      } catch (error) {
        console.error(`❌ [${requestId}] Error generating image for style ${style}:`, error);

        // Provide detailed error information
        let userFriendlyMessage = "Image generation failed";

        if (error instanceof Error) {
          console.error(`❌ [${requestId}] Error details for ${style}:`, {
            message: error.message,
            name: error.name,
            stack: error.stack?.substring(0, 500)
          });

          // Handle specific error types
          if (error.message.includes('insufficient_user_quota')) {
            userFriendlyMessage = "API quota exceeded. Please check your API credits.";
          } else if (error.message.includes('404')) {
            userFriendlyMessage = "API endpoint not found. Please check API configuration.";
          } else if (error.message.includes('401') || error.message.includes('403')) {
            userFriendlyMessage = "API authentication failed. Please check your API key.";
          } else if (error.message.includes('rate_limit')) {
            userFriendlyMessage = "API rate limit exceeded. Please try again later.";
          } else if (error.message.includes('content_policy_violation')) {
            userFriendlyMessage = "Content policy violation. Please try a different style.";
          }

          console.error(`❌ [${requestId}] User-friendly message for ${style}: ${userFriendlyMessage}`);
        } else {
          console.error(`❌ [${requestId}] Non-Error object thrown for ${style}:`, error);
        }

        // Create a simple colored placeholder instead of SVG
        const placeholderUrl = `data:image/svg+xml;base64,${Buffer.from(`
          <svg width="400" height="600" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#fef2f2"/>
            <rect x="20" y="20" width="360" height="560" fill="none" stroke="#fca5a5" stroke-width="2" stroke-dasharray="10,5"/>
            <text x="50%" y="40%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="18" fill="#dc2626" font-weight="bold">
              ${styleNames[style] || style}
            </text>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="14" fill="#ef4444">
              Generation Failed
            </text>
            <text x="50%" y="55%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="12" fill="#7f1d1d">
              ${userFriendlyMessage}
            </text>
            <text x="50%" y="70%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="10" fill="#991b1b">
              Please check API configuration
            </text>
          </svg>
        `).toString('base64')}`;

        generatedPhotos.push({
          id: `photo-${Date.now()}-${i}`,
          style: style,
          styleName: styleNames[style] || style,
          imageUrl: placeholderUrl,
          originalPhoto: photoUrl,
          createdAt: new Date().toISOString(),
          userId: userId || null,
          error: userFriendlyMessage
        });
      }
    }

    console.log(`🎉 [${requestId}] Completed generation for all styles. Generated ${generatedPhotos.length} photos`);

    const successfulPhotos = generatedPhotos.filter(photo => !photo.error);
    const failedPhotos = generatedPhotos.filter(photo => photo.error);

    console.log(`📊 [${requestId}] Generation summary:`, {
      total: generatedPhotos.length,
      successful: successfulPhotos.length,
      failed: failedPhotos.length,
      styles: generatedPhotos.map(p => ({ style: p.style, hasError: !!p.error }))
    });

    // 消费用户积分 - Consume user credits for successful generation
    if (userId && successfulPhotos.length > 0) {
      const creditsToConsume = successfulPhotos.length;
      const consumeSuccess = await consumeCredits(
        userId,
        creditsToConsume,
        `Generated ${creditsToConsume} AI wedding photos`,
        `生成了${creditsToConsume}张AI婚纱照`,
        {
          styleCount: creditsToConsume,
          generationId: `job-${Date.now()}`,
          styles: successfulPhotos.map(p => p.style)
        }
      );

      if (consumeSuccess) {
        console.log(`💰 [${requestId}] Successfully consumed ${creditsToConsume} credits for user: ${userId}`);
      } else {
        console.warn(`⚠️ [${requestId}] Failed to consume credits for user: ${userId}, but generation was successful`);
      }
    }

    const responseData = {
      success: true,
      jobId: `job-${Date.now()}`,
      photos: generatedPhotos,
      estimatedTime: "2-3 minutes",
      status: "completed",
      requestId,
      summary: {
        total: generatedPhotos.length,
        successful: successfulPhotos.length,
        failed: failedPhotos.length
      }
    };

    console.log(`📤 [${requestId}] Returning response with ${generatedPhotos.length} generated photos`);

    return NextResponse.json(responseData);

  } catch (error) {
    console.error(`❌ [${requestId}] Error in wedding photo generation API:`, error);
    console.error(`❌ [${requestId}] Error details:`, {
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack?.substring(0, 500) : undefined
    });

    return NextResponse.json({
      error: "Failed to generate wedding photos",
      details: error instanceof Error ? error.message : "Unknown error",
      requestId
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Wedding photo generation API",
    currentMode: AI_GENERATION_MODE,
    supportedModes: ["openai", "henapi", "apicore"],
    configuration: {
      mode: AI_GENERATION_MODE,
      hasOpenAIKey: !!process.env.OPENAI_API_KEY,
      hasHenAPIKey: !!process.env.HENAPI_API_KEY,
      hasAPICoreKey: !!process.env.APICORE_API_KEY,
      openaiBaseURL: process.env.OPENAI_BASE_URL || "https://api.laozhang.ai/v1",
      henApiBaseURL: process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1",
      apicoreBaseURL: process.env.APICORE_BASE_URL || "https://api.apicore.ai/v1"
    },
    supportedStyles: [
      "chinese-traditional",
      "western-elegant",
      "beach-sunset",
      "forest-romantic",
      "vintage-classic",
      "modern-chic"
    ]
  });
}
