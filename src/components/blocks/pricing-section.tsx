"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { SUBSCRIPTION_PLANS } from "@/lib/subscription"
import { SubscriptionPricingCard } from "@/components/ui/subscription-pricing-card"
import { Tab } from "@/components/ui/pricing-tab"

interface PricingSectionProps {
  title: string
  subtitle: string
}

export function PricingSection({
  title,
  subtitle,
}: PricingSectionProps) {
  const [selectedFrequency, setSelectedFrequency] = React.useState("monthly")
  const pathname = usePathname()
  const currentLocale = pathname.split('/')[1] || 'en'

  // Get all plans including free trial
  const allPlans = SUBSCRIPTION_PLANS

  return (
    <section className="flex flex-col items-center gap-10 py-10">
      <div className="space-y-7 text-center">
        <div className="space-y-4">
          <h1 className="text-4xl font-medium md:text-5xl">{title}</h1>
          <p className="text-muted-foreground">{subtitle}</p>
        </div>
        <div className="mx-auto flex w-fit rounded-full bg-muted p-1">
          <Tab
            text={currentLocale === 'zh' ? '月付' : 'Monthly'}
            selected={selectedFrequency === "monthly"}
            setSelected={() => setSelectedFrequency("monthly")}
          />
          <Tab
            text={currentLocale === 'zh' ? '年付' : 'Yearly'}
            selected={selectedFrequency === "yearly"}
            setSelected={() => setSelectedFrequency("yearly")}
            discount={true}
          />
        </div>
      </div>

      <div className="grid w-full max-w-6xl gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {allPlans.map((plan) => (
          <SubscriptionPricingCard
            key={plan.id}
            plan={plan}
            paymentFrequency={selectedFrequency}
            locale={currentLocale}
          />
        ))}
      </div>
    </section>
  )
}