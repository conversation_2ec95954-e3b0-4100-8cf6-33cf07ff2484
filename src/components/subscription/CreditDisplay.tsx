"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Zap,
  Plus,
  History,
  Crown,
  AlertCircle,
  TrendingUp
} from "lucide-react";
import { getUserSubscription, getUserCredits, getCreditUsageHistory, getSubscriptionPlan, type UserSubscription, type CreditUsage } from "@/lib/subscription";

interface CreditDisplayProps {
  userId: string;
  onUpgradeClick?: () => void;
  showUpgradeButton?: boolean;
  className?: string;
}

// 积分显示组件 - Credit display component
export function CreditDisplay({
  userId,
  onUpgradeClick,
  showUpgradeButton = true,
  className = ""
}: CreditDisplayProps) {
  // 状态管理 - State management
  const [credits, setCredits] = useState<number>(0);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [usageHistory, setUsageHistory] = useState<CreditUsage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 加载用户积分和订阅信息 - Load user credits and subscription info
  useEffect(() => {
    if (userId) {
      loadUserData();
    }
  }, [userId]);

  const loadUserData = async () => {
    setIsLoading(true);
    try {
      const [userCredits, userSubscription, creditHistory] = await Promise.all([
        getUserCredits(userId),
        getUserSubscription(userId),
        Promise.resolve(getCreditUsageHistory(userId))
      ]);

      setCredits(userCredits);
      setSubscription(userSubscription);
      setUsageHistory(creditHistory.slice(0, 5)); // 显示最近5条记录 - Show last 5 records
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取积分状态颜色 - Get credit status color
  const getCreditStatusColor = () => {
    if (credits === 0) return 'text-red-600 bg-red-100 border-red-200';
    if (credits <= 5) return 'text-yellow-600 bg-yellow-100 border-yellow-200';
    return 'text-green-600 bg-green-100 border-green-200';
  };

  // 获取积分状态图标 - Get credit status icon
  const getCreditStatusIcon = () => {
    if (credits === 0) return <AlertCircle className="h-4 w-4" />;
    if (credits <= 5) return <TrendingUp className="h-4 w-4" />;
    return <Zap className="h-4 w-4" />;
  };

  // 格式化日期 - Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取使用类型显示文本 - Get usage type display text
  const getUsageTypeText = (type: string) => {
    switch (type) {
      case 'generation': return '生成消费';
      case 'purchase': return '购买充值';
      case 'bonus': return '奖励获得';
      case 'refund': return '退款返还';
      default: return type;
    }
  };

  // 获取使用类型颜色 - Get usage type color
  const getUsageTypeColor = (type: string) => {
    switch (type) {
      case 'generation': return 'text-red-600';
      case 'purchase': return 'text-green-600';
      case 'bonus': return 'text-blue-600';
      case 'refund': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-pulse bg-gray-200 rounded-full h-8 w-20"></div>
      </div>
    );
  }

  const currentPlan = subscription ? getSubscriptionPlan(subscription.planId) : null;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* 积分显示 - Credits display */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={`border-2 transition-colors ${getCreditStatusColor()}`}
          >
            <div className="flex items-center space-x-2">
              {getCreditStatusIcon()}
              <span className="font-medium">{credits}</span>
              <span className="text-xs opacity-75">积分</span>
            </div>
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-80">
          {/* 当前订阅信息 - Current subscription info */}
          <div className="p-4 border-b">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">当前订阅</h4>
              {currentPlan && (
                <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                  {currentPlan.nameZh}
                </Badge>
              )}
            </div>
            <div className="text-sm text-gray-600">
              <div className="flex justify-between">
                <span>可用积分:</span>
                <span className="font-medium">{credits}</span>
              </div>
              {subscription && subscription.maxCredits > 0 && (
                <div className="flex justify-between">
                  <span>总积分:</span>
                  <span>{subscription.maxCredits}</span>
                </div>
              )}
            </div>
          </div>

          {/* 积分使用历史 - Credit usage history */}
          {usageHistory.length > 0 && (
            <>
              <div className="p-2">
                <div className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <History className="h-4 w-4 mr-2" />
                  最近使用记录
                </div>
                <div className="space-y-2">
                  {usageHistory.map((usage) => (
                    <div key={usage.id} className="flex items-center justify-between text-xs">
                      <div className="flex-1">
                        <div className="font-medium">{usage.descriptionZh}</div>
                        <div className="text-gray-500">{formatDate(usage.createdAt)}</div>
                      </div>
                      <div className={`font-medium ${getUsageTypeColor(usage.type)}`}>
                        {usage.amount > 0 ? '+' : ''}{usage.amount}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <DropdownMenuSeparator />
            </>
          )}

          {/* 升级按钮 - Upgrade button */}
          {showUpgradeButton && onUpgradeClick && (
            <DropdownMenuItem onClick={onUpgradeClick} className="p-3">
              <div className="flex items-center w-full">
                <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-3">
                  <Crown className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">升级订阅</div>
                  <div className="text-xs text-gray-500">获得更多积分和功能</div>
                </div>
                <Plus className="h-4 w-4 text-gray-400" />
              </div>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* 快速升级按钮 - Quick upgrade button */}
      {showUpgradeButton && onUpgradeClick && credits <= 5 && (
        <Button
          size="sm"
          onClick={onUpgradeClick}
          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
        >
          <Crown className="h-4 w-4 mr-1" />
          升级
        </Button>
      )}
    </div>
  );
}
