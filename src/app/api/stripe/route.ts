import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { config as authOptions } from '@/auth.config';

import { v4 as uuidv4 } from 'uuid';

// 延迟初始化Stripe，避免构建时的环境变量检查 - Lazy initialize Stripe to avoid build-time env var checks
function getStripe() {
  if (!process.env.STRIPE_PRIVATE_KEY) {
    throw new Error('STRIPE_PRIVATE_KEY is not set');
  }

  return new Stripe(process.env.STRIPE_PRIVATE_KEY, {
    apiVersion: '2025-02-24.acacia',
  });
}

export async function POST(request: Request) {
  try {
    // 检查Stripe环境变量 - Check Stripe environment variables
    if (!process.env.STRIPE_PRIVATE_KEY) {
      console.log('Stripe API: Stripe环境变量未配置');
      return NextResponse.json(
        { error: 'Stripe not configured' },
        { status: 503 }
      );
    }

    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      console.log('Stripe API: 认证失败，用户未登录');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      price,
      successUrl,
      cancelUrl,
      email,
      productName,
      stripePriceId,
      planId,
      paymentFrequency
    } = body;

    // Print received request data
    console.log('Received stripe payment request:', {
      price,
      email,
      successUrl,
      cancelUrl,
      productName,
      stripePriceId,
      planId,
      paymentFrequency,
      priceType: typeof price
    });

    // Validate required fields
    if (!price || !successUrl || !cancelUrl || !email) {
      console.log('Stripe API: 缺少必需字段');
      return NextResponse.json(
        { error: 'Missing required fields: price, successUrl, cancelUrl, email' },
        { status: 400 }
      );
    }

    // Ensure price is a number and convert to cents
    const amount = Math.round(parseFloat(price));
    console.log('计算金额:', price, '->', amount, '美分');

    if (isNaN(amount) || amount <= 0) {
      console.log('Stripe API: 价格无效', price);
      return NextResponse.json(
        { error: 'Invalid price amount' },
        { status: 400 }
      );
    }

    // Get user from database
    const user = await prisma.user.findFirst({
      where: {
        email: session.user.email,
      },
    });

    if (!user) {
      console.log('Stripe API: 未找到用户', session.user.email);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    console.log('准备创建Stripe会话');
    // Create Stripe checkout session
    const stripe = getStripe();

    // Determine if this is a subscription or one-time payment
    const isSubscription = stripePriceId && (planId === 'basic' || planId === 'premium');

    let sessionConfig: any = {
      payment_method_types: ['card'],
      customer_email: email,
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        planId: planId || 'unknown',
        paymentFrequency: paymentFrequency || 'monthly',
        userId: user.uuid,
      },
    };

    if (isSubscription && stripePriceId) {
      // Subscription mode
      console.log('创建订阅会话，价格ID:', stripePriceId);
      sessionConfig = {
        ...sessionConfig,
        mode: 'subscription',
        line_items: [
          {
            price: stripePriceId,
            quantity: 1,
          },
        ],
        subscription_data: {
          metadata: {
            planId: planId,
            paymentFrequency: paymentFrequency,
            userId: user.uuid,
          },
        },
      };
    } else {
      // One-time payment mode
      console.log('创建一次性支付会话，金额:', amount);
      sessionConfig = {
        ...sessionConfig,
        mode: 'payment',
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: productName || 'Purchase',
                metadata: {
                  planId: planId || 'unknown',
                  paymentFrequency: paymentFrequency || 'one-time',
                },
              },
              unit_amount: amount,
            },
            quantity: 1,
          },
        ],
      };
    }

    const stripeSession = await stripe.checkout.sessions.create(sessionConfig);
    console.log('Stripe会话创建成功:', stripeSession.id);

    console.log('创建订单记录');
    // Determine credits based on plan
    const getCreditsForPlan = (planId: string) => {
      switch (planId) {
        case 'free': return 3;
        case 'basic': return 20;
        case 'premium': return 150;
        default: return 1;
      }
    };

    // Create order in database
    await prisma.order.create({
      data: {
        orderNo: uuidv4(),
        userUuid: user.uuid,
        userEmail: user.email,
        amount: amount,
        status: 'pending',
        stripeSessionId: stripeSession.id,
        credits: getCreditsForPlan(planId || 'unknown'),
        currency: 'usd',
        productName: productName || 'Purchase',
        createdAt: new Date(),
        // Add additional metadata
        metadata: JSON.stringify({
          planId: planId || 'unknown',
          paymentFrequency: paymentFrequency || 'one-time',
          isSubscription: isSubscription,
          stripePriceId: stripePriceId || null,
        }),
      },
    });
    console.log('订单记录创建成功');

    // 返回 Stripe 结账 URL
    console.log('返回支付URL:', stripeSession.url);
    return NextResponse.json({ url: stripeSession.url });
    
  } catch (error: any) {
    console.error('Error processing payment:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
